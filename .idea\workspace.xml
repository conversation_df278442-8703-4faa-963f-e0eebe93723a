<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="fb564974-2663-42e0-a679-b564ef2f79d1" name="Changes" comment="楼宇信息-零售租户修改">
      <change beforePath="$PROJECT_DIR$/src/views/login/login.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/login/login.vue" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="master" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
    <option name="RESET_MODE" value="KEEP" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 1
}</component>
  <component name="ProjectId" id="2tYY1lSeIJ6v57eAH1DSmaVZoKT" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Node.js.vite.config.js.executor": "Debug",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "git-widget-placeholder": "UI-MCP-test",
    "last_opened_file_path": "D:/workspace_sm/gitee/biaobiaozhun_website",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "ts.external.directory.path": "D:\\software\\WebStorm 2024.2.0.1\\plugins\\javascript-plugin\\jsLanguageServicesImpl\\external",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-410509235cf1-JavaScript-WS-242.20224.426" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="fb564974-2663-42e0-a679-b564ef2f79d1" name="Changes" comment="" />
      <created>1740530262065</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1740530262065</updated>
      <workItem from="1740530263172" duration="8000" />
      <workItem from="1740530284924" duration="3828000" />
      <workItem from="1740640979895" duration="1200000" />
      <workItem from="1740737679263" duration="5266000" />
      <workItem from="1740985255828" duration="670000" />
      <workItem from="1741048475725" duration="2112000" />
      <workItem from="1741076310017" duration="4054000" />
      <workItem from="1741329921535" duration="9000" />
      <workItem from="1741571440386" duration="684000" />
      <workItem from="1741680092909" duration="1680000" />
      <workItem from="1741742856241" duration="22000" />
      <workItem from="1741913766789" duration="385000" />
      <workItem from="1742203262353" duration="2459000" />
      <workItem from="1742285321105" duration="1919000" />
      <workItem from="1743391854753" duration="926000" />
      <workItem from="1743468776100" duration="13000" />
      <workItem from="1743586980256" duration="4529000" />
      <workItem from="1744245319073" duration="1773000" />
      <workItem from="1744684113381" duration="90000" />
      <workItem from="1745456031787" duration="2996000" />
      <workItem from="1745832539652" duration="1564000" />
      <workItem from="1745991578950" duration="22000" />
      <workItem from="1747795809986" duration="1907000" />
      <workItem from="1748410766506" duration="28000" />
      <workItem from="1748410820623" duration="792000" />
      <workItem from="1748490922941" duration="9000" />
      <workItem from="1749189818342" duration="1974000" />
      <workItem from="1749618288341" duration="50000" />
      <workItem from="1749619714523" duration="170000" />
      <workItem from="1749629568309" duration="861000" />
      <workItem from="1749632746368" duration="510000" />
      <workItem from="1749633263392" duration="143000" />
      <workItem from="1749698815613" duration="106000" />
      <workItem from="1749800851810" duration="5000" />
      <workItem from="1750390858445" duration="2795000" />
      <workItem from="1750642945231" duration="14079000" />
      <workItem from="1750909712755" duration="59000" />
      <workItem from="1750910039631" duration="2415000" />
      <workItem from="1750927112363" duration="3134000" />
      <workItem from="1751865041561" duration="4284000" />
      <workItem from="1751945418594" duration="3421000" />
      <workItem from="1752030067154" duration="3064000" />
      <workItem from="1752107454373" duration="6105000" />
      <workItem from="1752461984271" duration="8014000" />
    </task>
    <task id="LOCAL-00001" summary="备案号添加">
      <option name="closed" value="true" />
      <created>1747795961959</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1747795961959</updated>
    </task>
    <task id="LOCAL-00002" summary="隐藏介绍页付费模块">
      <option name="closed" value="true" />
      <created>1748411078332</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1748411078332</updated>
    </task>
    <task id="LOCAL-00003" summary="名称修改">
      <option name="closed" value="true" />
      <created>1749190246872</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1749190246872</updated>
    </task>
    <task id="LOCAL-00004" summary="名称修改">
      <option name="closed" value="true" />
      <created>1750915933434</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1750915933434</updated>
    </task>
    <task id="LOCAL-00005" summary="商宇通【地产金融-REITs/ABS-行业概况】表格字段按照图片调整，PC端和移动端同步调整，其中“当前价格”取REITs的“最新收盘价”">
      <option name="closed" value="true" />
      <created>1752119598880</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1752119598881</updated>
    </task>
    <task id="LOCAL-00006" summary="楼宇信息-零售租户修改">
      <option name="closed" value="true" />
      <created>1752474098847</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1752474098847</updated>
    </task>
    <option name="localTasksCounter" value="7" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.History.Properties">
    <option name="COLUMN_ID_ORDER">
      <list>
        <option value="Default.Root" />
        <option value="Default.Author" />
        <option value="Default.Date" />
        <option value="Default.Subject" />
        <option value="GitHub.CommitStatus" />
      </list>
    </option>
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="OPEN_GENERIC_TABS">
      <map>
        <entry key="4aa9a8d2-d8dd-44db-8620-1111e8ce25bb" value="TOOL_WINDOW" />
        <entry key="fcb1d0d9-2db9-49a0-9d0c-81e99b8754aa" value="TOOL_WINDOW" />
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="4aa9a8d2-d8dd-44db-8620-1111e8ce25bb">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                  <entry key="roots">
                    <value>
                      <list>
                        <option value="$PROJECT_DIR$" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
        <entry key="fcb1d0d9-2db9-49a0-9d0c-81e99b8754aa">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                  <entry key="roots">
                    <value>
                      <list>
                        <option value="$PROJECT_DIR$" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="备案号添加" />
    <MESSAGE value="隐藏介绍页付费模块" />
    <MESSAGE value="名称修改" />
    <MESSAGE value="商宇通【地产金融-REITs/ABS-行业概况】表格字段按照图片调整，PC端和移动端同步调整，其中“当前价格”取REITs的“最新收盘价”" />
    <MESSAGE value="楼宇信息-零售租户修改" />
    <option name="LAST_COMMIT_MESSAGE" value="楼宇信息-零售租户修改" />
  </component>
</project>