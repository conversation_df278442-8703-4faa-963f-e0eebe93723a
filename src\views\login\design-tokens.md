# 登录页面设计规范

基于Figma设计提取的设计token和规范

## 颜色规范

### 主要颜色
- 主背景色: `#DDE6F4`
- 白色卡片: `#FFFFFF`
- 主色调蓝色: `#1868F1`
- 文字主色: `#1D2129`
- 文字次色: `#4E5969`
- 占位符文字: `#86909C`
- 边框色: `#E5E6EB`
- 分割线色: `#C9CDD4`
- 渐变色: `#1392FF` → `#8047FF`
- 青色装饰: `#63E2E3`

### 透明度
- 背景模糊效果: `rgba(221, 230, 244, 0.3)`
- 阴影: `rgba(0, 0, 0, 0.05)`

## 尺寸规范

### 页面布局
- 页面尺寸: `1920x980px`
- 登录卡片: `478x594px`
- 卡片圆角: `20px`
- 卡片内边距: `48px`

### 输入框
- 高度: `44px` (padding: 10px 12px)
- 边框圆角: `4px`
- 边框宽度: `1px`

### 按钮
- 登录按钮高度: `44px` (padding: 10px)
- 按钮圆角: `4px`

### 复选框
- 尺寸: `14x14px`
- 圆角: `2px`

### 图标
- 密码显示/隐藏图标: `16x16px`
- 客户服务图标: `20x20px`

## 字体规范

### 字体族
- 主字体: `PingFang SC`

### 字体大小和权重
- 主标题: `24px, font-weight: 500, line-height: 1.5em`
- 副标题: `26px, font-weight: 600, line-height: 1.54em`
- 正文: `16px, font-weight: 500, line-height: 1.5em`
- 小字: `14px, font-weight: 400, line-height: 1.57em`
- 最小字: `12px, font-weight: 400, line-height: 1.5em`

## 间距规范

### 组件间距
- 表单项间距: `20px`
- 大区块间距: `32px`
- 标题区域间距: `40px`
- 整体内容间距: `80px`

### 内边距
- 卡片内边距: `48px`
- 输入框内边距: `10px 12px`
- 按钮内边距: `10px`

## 布局规范

### 登录卡片定位
- 位置: 右侧居中
- 距离右边: 约320px
- 垂直居中

### 标签切换
- 标签间距: `36px`
- 下划线高度: `2px`
- 下划线颜色: `#1868F1`

## 特殊效果

### 阴影
- 卡片阴影: `0px 5px 15px 0px rgba(0, 0, 0, 0.05)`
- 背景模糊: `blur(100px)`, `backdrop-filter: blur(4px)`

### 渐变
- 装饰渐变: `linear-gradient(90deg, #1392FF 0%, #8047FF 100%)`
- 背景渐变: 多层渐变效果

## 交互状态

### 输入框
- 默认边框: `#E5E6EB`
- 聚焦状态: 需要定义
- 错误状态: 需要定义

### 按钮
- 默认: `#1868F1`
- 悬停: 需要定义
- 点击: 需要定义

### 链接
- 默认: `#4E5969`
- 悬停: 需要定义

## 组件规范

### 复选框
- 未选中: 白色背景，透明边框
- 选中: `#1868F1`背景，白色勾选图标

### 验证码按钮
- 样式: 文字按钮
- 颜色: `#1868F1`
- 对齐: 右对齐
