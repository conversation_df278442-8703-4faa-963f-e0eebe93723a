<template>
	<div class="login-page">
		<!-- 背景装饰区域 -->
		<div class="background-decorations">
			<!-- 背景图片 -->
			<div class="background-image">
				<img src="@/assets/platform.png" alt="" />
			</div>

			<!-- 渐变装饰 -->
			<div class="gradient-overlay-top"></div>
			<div class="gradient-overlay-left"></div>
			<div class="gradient-overlay-bottom"></div>

			<!-- 模糊装饰圆 -->
			<div class="blur-circle"></div>

			<!-- 品牌标识区域 -->
			<div class="brand-section">
				<div class="brand-vector"></div>
				<div class="brand-text">商业地产</div>
			</div>
		</div>

		<!-- 顶部导航栏 -->
		<div class="top-header">
			<div class="header-content">
				<div class="header-left" @click="router.push('/')">
					<div class="logo-container">
						<img src="@/assets/newLogo.png" alt="logo" />
					</div>
					<div class="brand-info">
						<div class="brand-name">术木智能</div>
						<div class="brand-subtitle">·商业地产价值分析平台</div>
					</div>
				</div>
				<div class="header-right" @click="router.push('/relation')">
					<div class="service-icon">
						<svg width="20" height="20" viewBox="0 0 20 20" fill="none">
							<path d="M15 6.67V13.33" stroke="#1D2129" stroke-width="1.67"/>
							<path d="M1.67 6.67V13.33" stroke="#1D2129" stroke-width="1.67"/>
							<path d="M5 1.67V16.67" stroke="#1D2129" stroke-width="1.67"/>
						</svg>
					</div>
					<div class="service-text">客户服务</div>
				</div>
			</div>
		</div>

		<!-- 协议返回按钮 -->
		<div class="protocol-return" v-if="agreeStatus">
			<div @click="handleSuerAgree(0)">
				<el-icon><ArrowLeft /></el-icon>
				<div>返回</div>
			</div>
		</div>

		<!-- 主要内容区域 -->
		<div class="main-content" v-if="!agreeStatus">
			<!-- 登录卡片 -->
			<div class="login-card">
				<div class="card-content">
					<!-- 欢迎标题 -->
					<div class="welcome-section">
						<div class="welcome-title">
							您好，欢迎来到<br />术木智能·商业地产价值分析平台
						</div>

						<!-- 登录方式切换 -->
						<div class="login-tabs">
							<div class="tab-item" :class="{ active: indexActive === 1 }" @click="handleBtnClick({index: 1})">
								<div class="tab-text">账号登录</div>
								<div class="tab-underline"></div>
							</div>
							<div class="tab-item" :class="{ active: indexActive === 2 }" @click="handleBtnClick({index: 2})">
								<div class="tab-text">手机号登录</div>
								<div class="tab-underline"></div>
							</div>
						</div>
					</div>

				<!-- 登录表单 -->
				<div class="login-form-section">
					<el-form :model="ruleForm" ref="ruleForms" class="login-form">
						<!-- 用户名输入框 (账号登录) -->
						<el-form-item v-if="indexActive === 1" prop="userName" class="form-item">
							<el-input
								v-model="ruleForm.userName"
								maxlength="14"
								placeholder="请输入用户名"
								class="custom-input"
							/>
						</el-form-item>

						<!-- 手机号输入框 (手机号登录) -->
						<el-form-item v-if="indexActive === 2" prop="phone" class="form-item">
							<el-input
								v-model="ruleForm.phone"
								maxlength="11"
								placeholder="请输入手机号"
								class="custom-input"
							/>
						</el-form-item>

						<!-- 密码输入框 (账号登录) -->
						<el-form-item v-if="indexActive === 1" prop="password" class="form-item password-item">
							<el-input
								v-model="ruleForm.password"
								:type="password ? 'password' : 'text'"
								maxlength="20"
								placeholder="请输入登录密码"
								class="custom-input"
							>
								<template #suffix>
									<div class="password-toggle" @click="showPwd">
										<svg v-if="password" width="16" height="16" viewBox="0 0 16 16" fill="none">
											<path d="M1.33 4L14.67 12" stroke="#4E5969" stroke-width="1.33"/>
											<path d="M6.33 6.87L9.16 9.66" stroke="#4E5969" stroke-width="1.33"/>
											<path d="M2 2L14 14" stroke="#4E5969" stroke-width="1.33"/>
										</svg>
										<svg v-else width="16" height="16" viewBox="0 0 16 16" fill="none">
											<path d="M1.33 4L14.67 12" stroke="#4E5969" stroke-width="1.33"/>
											<path d="M6.33 6.87L9.16 9.66" stroke="#4E5969" stroke-width="1.33"/>
											<path d="M2 2L14 14" stroke="#4E5969" stroke-width="1.33"/>
										</svg>
									</div>
								</template>
							</el-input>
						</el-form-item>

						<!-- 验证码输入框 (手机号登录) -->
						<el-form-item v-if="indexActive === 2" prop="code" class="form-item verification-item">
							<el-input
								v-model="ruleForm.code"
								maxlength="4"
								placeholder="请输入验证码"
								class="custom-input"
							>
								<template #suffix>
									<div class="verification-code-btn" @click="onGetCode()" v-if="isSend">
										获取验证码
									</div>
									<div class="verification-code-countdown" v-else>
										获取验证码{{ time }}s
									</div>
								</template>
							</el-input>
						</el-form-item>

						<!-- 同意协议复选框 -->
						<div class="agreement-section">
							<div class="custom-checkbox" :class="{ checked: formLabelAlign.agreeAgreement }" @click="toggleAgreement">
								<div class="checkbox-icon">
									<svg v-if="formLabelAlign.agreeAgreement" width="8" height="8" viewBox="0 0 8 8" fill="none">
										<path d="M1 4L3 6L7 2" stroke="white" stroke-width="1.5"/>
									</svg>
								</div>
							</div>
							<div class="agreement-text">
								我已阅读并同意
								<span class="agreement-link" @click="handleSuerAgree(1)">用户协议</span> 、
								<span class="agreement-link" @click="handleSuerAgree(1)">隐私政策</span> 和
								<span class="agreement-link" @click="handleSuerAgree(2)">产品使用条款</span>
							</div>
						</div>

						<!-- 登录按钮 -->
						<el-button
							type="primary"
							@click="handleLogin(indexActive)"
							class="login-button"
						>
							登录
						</el-button>
					</el-form>
				</div>

					<!-- 底部链接 -->
					<div class="footer-links">
						<span class="footer-link" @click="handleBtnClick({index: 3})">忘记密码</span>
						<span class="footer-divider">｜</span>
						<span class="footer-link" @click="handleBtnClick({index: 4})">注册账号</span>
					</div>
				</div>
			</div>
		</div>

		<!-- 协议内容显示区域 -->
		<div v-else class="agreement-content">
			<div class="agreement-container">
				<privacy v-if="agreeStatus === 1"></privacy>
				<agreement v-if="agreeStatus === 2" :returnShow="false"></agreement>
			</div>
		</div>
	</div>
</template>

<script setup>
import { useRoute, useRouter } from 'vue-router';
import privacy from './privacy.vue';
import agreement from './agreement.vue';
import { useStore, vuexStore } from '../../store';
import { getCodes, getRegister, getLogin, getForget } from 'REQUEST_API';
import { ElMessage } from 'element-plus';
const store = useStore();
const route = useRoute();
const ruleForm = reactive({
	// 用户名
	userName: null,
	// 手机号码
	phone: null,
	// 验证码
	code: null,
	// 密码
	password: null,
	// 确认密码
	passwords: null,
});

const formLabelAlign = reactive({
	// 是否同意协议
	agreeAgreement: false,
	listOption: [
		{
			name: '密码登录',
			index: 1,
		},
		{
			name: '验证码登录',
			index: 2,
		},
		{
			name: '找回密码',
			index: 3,
		},
		{
			name: '注册账户',
			index: 4,
		},
	],
});

// dom
const ruleForms = ref(null);
// 验证码秒数
const time = ref(60);
// 获取验证码
const isSend = ref(true);
// 控制展示内容
const indexActive = ref(1);

// 控制协议展示状态
const agreeStatus = ref(0);

const router = useRouter();

let password = ref(true);

let passwords = ref(true);

// 页面加载
onMounted(() => {
	vuexStore.dispatch('handleDistrict'); // 获取区域
});

const showPwd = () => {
	password.value = !password.value;
};

const showPwds = () => {
	passwords.value = !passwords.value;
};

// 切换协议同意状态
const toggleAgreement = () => {
	formLabelAlign.agreeAgreement = !formLabelAlign.agreeAgreement;
};

/**
 * @function handleBtnClick 切换登录页展示内容
 */
function handleBtnClick(item, idx) {
	formLabelAlign.agreeAgreement = false;
	passwords.value = true;
	password.value = true;
	ruleForms.value.resetFields();
	if (idx) {
		indexActive.value = idx;
	} else {
		indexActive.value = item.index;
	}
}

/**
 * @function handleLogin
 * @index 1 密码登录 2 验证码登录 3 找回密码 4 注册账户
 */
function handleLogin(index) {
	if (index === 1 && handleAgree()) {
		// 用户名密码验证
		if (!ruleForm.userName) {
			ElMessage.warning('请输入用户名');
			return;
		}

		// 用户名密码验证
		if (!ruleForm.password) {
			ElMessage.warning('请输入密码');
			return;
		}

		handleLogins({ type: 'PASSWORD' });
		return;
	}
	if (index === 2 && handleAgree()) {
		// 手机号码验证
		if (!ruleForm.phone) {
			ElMessage.warning('请输入手机号');
			return;
		}
		//验证码验证
		if (!ruleForm.code) {
			ElMessage.warning('请输入验证码');
			return;
		}
		handleLogins({ type: 'PHONE' });
		return;
	}
	if (index === 3 && handleAgree(3)) {
		// 找回密码
		if (!ruleForm.phone) {
			ElMessage.warning('请输入手机号');
			return;
		}
		// 密码验证6-20个字符，需包含字母、数字，不能包含空格
		if (!ruleForm.password) {
			ElMessage.warning('请输入密码');
			return;
		}
		if (ruleForm.password.length < 6 || ruleForm.password.length > 20) {
			ElMessage.warning('密码长度6-20个字符');
			return;
		}
		if (!/^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{6,20}$/.test(ruleForm.password)) {
			ElMessage.warning('密码需包含字母、数字，不能包含空格');
			return;
		}

		handlegetForget();
		return;
	}

	if (index === 4 && handleAgree()) {
		// 用户注册
		if (!ruleForm.userName) {
			ElMessage.warning('请输入用户名');
			return;
		}
		// 验证密码
		if (!ruleForm.password) {
			ElMessage.warning('请输入密码');
			return;
		}
		// 比较两次密码
		if (ruleForm.password !== ruleForm.passwords) {
			ElMessage.warning('两次密码不一致');
			return;
		}
		handlegetRegister();
	}
}
// 用户名登录 / 密码登录
function handleLogins(param) {
	getLogin({ ...ruleForm, ...param }).then((res) => {
		handleUpdate(res);
	});
}
// 忘记密码
function handlegetForget() {
	getForget({ ...ruleForm }).then((res) => {
		// handleUpdate(res);
		if (res.code == 200) {
			ElMessage({
				message: '密码重置成功',
				type: 'success',
			});
			handleBtnClick({}, 1);
		}
	});
}

// 用户注册
function handlegetRegister() {
	getRegister({ ...ruleForm }).then((res) => {
		handleUpdate(res);
	});
}

// 更新数据
function handleUpdate(res) {
	if (!res) return;
	if (res && res.code == 200) {
		window.localStorage.setItem('token', res.data.accessToken); // 设置token
		store.getUserInfo(); // 获取用户信息
		vuexStore.dispatch('handleGetShoppingCart'); // 获取购物车
		vuexStore.dispatch('handleDistrict'); // 获取区域
		vuexStore.commit('handleNewUserCoupon', res.data.sendSytNewUserCoupon); // 新用户专享优惠券
		ElMessage({
			message: '登录成功',
			type: 'success',
		});
		if (window.history.length === 1) {
			router.push('/');
			// 当前页面是历史记录中的最后一页
		} else {
			if (window.history.state.back !== '/login') {
				if (route.query['0'] === '1') {
					router.push('/');
				} else {
					router.push(window.history.state.back);
				}
			} else {
				router.push('/');
			}
		}
	} else if (res.code == 500 || res.code == 2005) {
		// ElMessage.error(res.message);
	} else if (res.code == 1002) {
		ElMessage.error('请您先点击获取验证码再做操作');
	}
}

// 效验用户协议 type 3 找回密码
function handleAgree(type) {
	if (type === 3) {
		if (ruleForm.password !== ruleForm.passwords) {
			ElMessage.warning('请输入相同的密码');
			return false;
		}
	} else {
		if (!formLabelAlign.agreeAgreement) {
			ElMessage.warning('请先同意用户协议');
			return false;
		}
	}
	return true;
}

// 获取验证码
const onGetCode = async () => {
	const phone = ruleForm.phone;
	if (!phone) {
		return ElMessage({
			message: '请输入手机号',
			type: 'warning',
		});
	}
	// 校验手机号
	const reg = /^1[3-9]\d{9}$/;
	if (!reg.test(phone)) {
		return ElMessage({
			message: '请输入正确的手机号',
			type: 'warning',
		});
	}
	// console.log('获取验证码');
	isSend.value = false;
	const timer = setInterval(() => {
		time.value--;
		if (time.value == 0) {
			clearInterval(timer);
			isSend.value = true;
			time.value = 60;
		}
	}, 1000);

	// 发送验证码
	await getCodes({ phone: ruleForm.phone })
		.then((res) => {
			console.log(res);
			// 发送成功提示
			ElMessage({
				message: '验证码发送成功,请注意查收~',
				type: 'success',
			});
		})
		.catch((err) => {
			console.log('err', err);
			// 显示错误信息给用户
		});
};
// 产品使用条款
const handlePrivacyAgree = () => {
	// router.push('/privacy');
};
// 用户协议  index 1 用户协议 2 产品使用条款
const handleSuerAgree = (index) => {
	agreeStatus.value = index;
	// router.push('/agreement');
};
</script>

<style lang="less" scoped>
/* CSS变量定义 */
:root {
	--primary-blue: #1868F1;
	--text-primary: #1D2129;
	--text-secondary: #4E5969;
	--text-placeholder: #86909C;
	--border-color: #E5E6EB;
	--divider-color: #C9CDD4;
	--background-color: #DDE6F4;
	--white: #FFFFFF;
	--gradient-start: #1392FF;
	--gradient-end: #8047FF;
	--cyan-accent: #63E2E3;
	--blur-bg: rgba(221, 230, 244, 0.3);
	--shadow-color: rgba(0, 0, 0, 0.05);
}

/* 全局重置 */
* {
	box-sizing: border-box;
}

/* 主容器 */
.login-page {
	position: relative;
	width: 100vw;
	height: 100vh;
	background: var(--background-color);
	overflow: hidden;
	font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 背景装饰区域 */
.background-decorations {
	position: fixed;
	top: 0;
	left: 0;
	width: 100vw;
	height: 100vh;
	z-index: 0;
	pointer-events: none;

	.background-image {
		position: absolute;
		left: 5%;
		top: 0;
		width: 87%;
		height: 100%;
		opacity: 0.8;

		img {
			width: 100%;
			height: 100%;
			object-fit: cover;
		}
	}

	.gradient-overlay-top {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 20%;
		background: linear-gradient(180deg, var(--background-color) 0%, transparent 100%);
	}

	.gradient-overlay-left {
		position: absolute;
		top: 0;
		left: 0;
		width: 15%;
		height: 100%;
		background: linear-gradient(90deg, var(--background-color) 20%, transparent 100%);
	}

	.gradient-overlay-bottom {
		position: absolute;
		bottom: 0;
		left: 0;
		width: 100%;
		height: 20%;
		background: linear-gradient(0deg, var(--background-color) 0%, transparent 100%);
	}

	.blur-circle {
		position: absolute;
		left: 15%;
		top: 20%;
		width: 40%;
		height: 15%;
		background: var(--blur-bg);
		border-radius: 50%;
		filter: blur(80px);
		backdrop-filter: blur(4px);
	}

	.brand-section {
		position: absolute;
		left: 20%;
		top: 25%;
		width: 35%;
		height: 8%;

		.brand-vector {
			position: absolute;
			right: 0;
			top: 15%;
			width: 60%;
			height: 70%;
			background: linear-gradient(90deg, var(--gradient-start) 0%, var(--gradient-end) 100%);
			border-radius: 4px;
		}

		.brand-text {
			position: absolute;
			left: 0;
			top: 0;
			width: 40%;
			height: 100%;
			font-size: clamp(24px, 3vw, 48px);
			font-weight: 600;
			line-height: 1.4;
			letter-spacing: 0.12em;
			color: var(--text-primary);
			-webkit-text-stroke: 0.5px var(--text-primary);
			display: flex;
			align-items: center;
		}
	}
}

/* 顶部导航栏 */
.top-header {
	position: absolute;
	top: 20px;
	left: 0;
	right: 0;
	z-index: 20;

	.header-content {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 0 40px;

		.header-left {
			display: flex;
			align-items: center;
			cursor: pointer;

			.logo-container {
				width: 40px;
				height: 40px;
				margin-right: 8px;

				img {
					width: 100%;
					height: 100%;
					object-fit: contain;
				}
			}

			.brand-info {
				.brand-name {
					font-size: 26px;
					font-weight: 600;
					line-height: 1.54;
					color: var(--text-primary);
				}

				.brand-subtitle {
					font-size: 16px;
					font-weight: 500;
					line-height: 1.5;
					color: var(--text-primary);
				}
			}
		}

		.header-right {
			display: flex;
			align-items: center;
			gap: 4px;
			cursor: pointer;

			.service-icon {
				width: 20px;
				height: 20px;
			}

			.service-text {
				font-size: 16px;
				font-weight: 500;
				line-height: 1.5;
				color: var(--text-primary);
			}
		}
	}
}

/* 协议返回按钮 */
.protocol-return {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 48px;
	display: flex;
	justify-content: center;
	align-items: center;
	background: var(--white);
	box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.06);
	z-index: 10;

	div {
		display: flex;
		align-items: center;
		font-size: 16px;
		font-weight: 700;
		color: var(--text-placeholder);
		cursor: pointer;

		div {
			margin-left: 5px;
		}
	}
}

/* 主要内容区域 */
.main-content {
	position: relative;
	width: 100%;
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: flex-end;
	padding-right: 8%;
	z-index: 10;
}

/* 登录卡片 */
.login-card {
	width: min(478px, 90vw);
	max-width: 478px;
	min-height: 594px;
	background: var(--white);
	border-radius: 20px;
	box-shadow: 0px 5px 15px 0px var(--shadow-color);
	position: relative;

	.card-content {
		padding: 48px;
		height: 100%;
		display: flex;
		flex-direction: column;
		gap: 80px;

		.welcome-section {
			display: flex;
			flex-direction: column;
			gap: 40px;

			.welcome-title {
				font-size: 24px;
				font-weight: 500;
				line-height: 1.5;
				color: var(--text-primary);
			}

			.login-tabs {
				display: flex;
				align-items: center;
				gap: 36px;

				.tab-item {
					display: flex;
					flex-direction: column;
					gap: 4px;
					cursor: pointer;

					.tab-text {
						font-size: 16px;
						font-weight: 500;
						line-height: 1.5;
						color: var(--text-secondary);
						transition: color 0.3s ease;
					}

					.tab-underline {
						height: 2px;
						background: transparent;
						transition: background-color 0.3s ease;
					}

					&.active {
						.tab-text {
							color: var(--primary-blue);
						}

						.tab-underline {
							background: var(--primary-blue);
						}
					}
				}
			}
		}
	}
}

/* 登录表单样式 */
.login-form-section {
	flex: 1;

	.login-form {
		display: flex;
		flex-direction: column;
		gap: 20px;

		.form-item {
			margin-bottom: 0;

			:deep(.el-form-item__content) {
				margin-left: 0 !important;
			}

			.custom-input {
				:deep(.el-input__wrapper) {
					padding: 10px 12px;
					border: 1px solid var(--border-color);
					border-radius: 4px;
					box-shadow: none;
					background: var(--white);

					.el-input__inner {
						font-size: 14px;
						font-weight: 400;
						line-height: 1.57;
						color: var(--text-primary);

						&::placeholder {
							color: var(--text-placeholder);
						}
					}
				}

				:deep(.el-input__wrapper.is-focus) {
					border-color: var(--primary-blue);
				}
			}

			&.password-item {
				.password-toggle {
					display: flex;
					align-items: center;
					cursor: pointer;
					padding: 2px;
				}
			}

			&.verification-item {
				.verification-code-btn {
					font-size: 14px;
					font-weight: 400;
					line-height: 1.57;
					color: var(--primary-blue);
					cursor: pointer;
					text-align: right;
				}

				.verification-code-countdown {
					font-size: 14px;
					font-weight: 400;
					line-height: 1.57;
					color: var(--text-placeholder);
					text-align: right;
				}
			}
		}

		.agreement-section {
			display: flex;
			align-items: flex-start;
			gap: 4px;
			margin: 16px 0;

			.custom-checkbox {
				width: 14px;
				height: 14px;
				border: 1px solid transparent;
				border-radius: 2px;
				background: var(--white);
				cursor: pointer;
				display: flex;
				align-items: center;
				justify-content: center;
				flex-shrink: 0;
				margin-top: 1px;

				&.checked {
					background: var(--primary-blue);

					.checkbox-icon {
						display: block;
					}
				}

				.checkbox-icon {
					display: none;
				}
			}

			.agreement-text {
				font-size: 12px;
				font-weight: 400;
				line-height: 1.5;
				color: var(--text-secondary);

				.agreement-link {
					color: var(--primary-blue);
					cursor: pointer;
					text-decoration: none;

					&:hover {
						text-decoration: underline;
					}
				}
			}
		}

		.login-button {
			width: 100%;
			height: 44px;
			background: var(--primary-blue);
			border: none;
			border-radius: 4px;
			font-size: 14px;
			font-weight: 500;
			line-height: 1.57;
			color: var(--white);
			cursor: pointer;

			&:hover {
				background: #0056d3;
			}
		}
	}
}

/* 底部链接 */
.footer-links {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 18px;

	.footer-link {
		font-size: 14px;
		font-weight: 400;
		line-height: 1.57;
		color: var(--text-secondary);
		cursor: pointer;

		&:hover {
			color: var(--primary-blue);
		}
	}

	.footer-divider {
		font-size: 14px;
		font-weight: 400;
		line-height: 1.57;
		color: var(--divider-color);
	}
}

/* 协议内容显示区域 */
.agreement-content {
	position: absolute;
	top: 48px;
	left: 0;
	right: 0;
	bottom: 0;
	background: var(--white);
	overflow-y: auto;
	z-index: 10;

	.agreement-container {
		max-width: 60%;
		margin: 0 auto;
		padding: 40px 20px;
	}
}

/* 响应式设计 */
@media only screen and (max-width: 1440px) {
	.main-content {
		padding-right: 200px;
	}

	.login-card {
		width: 420px;
		height: 520px;

		.card-content {
			padding: 40px;
			gap: 60px;
		}
	}
}

@media only screen and (max-width: 1200px) {
	.main-content {
		padding-right: 100px;
	}

	.background-decorations {
		.brand-section {
			display: none;
		}
	}
}

@media only screen and (max-width: 768px) {
	.login-page {
		padding: 20px;
	}

	.main-content {
		padding: 0;
		justify-content: center;
	}

	.login-card {
		width: 100%;
		max-width: 400px;
		height: auto;
		min-height: 500px;

		.card-content {
			padding: 32px;
			gap: 40px;
		}
	}

	.background-decorations {
		.background-image {
			opacity: 0.3;
		}
	}

	.top-header {
		.header-content {
			padding: 0 20px;

			.header-left {
				.brand-info {
					.brand-name {
						font-size: 20px;
					}

					.brand-subtitle {
						font-size: 14px;
					}
				}
			}
		}
	}
}

/* Element Plus 组件样式重置 */
:deep(.el-form-item__label) {
	display: none !important;
}

:deep(.el-button) {
	border: none;

	&:focus, &:hover {
		border: none;
	}
}

/* 响应式设计 */
@media (max-width: 1200px) {
	.main-content {
		padding-right: 5%;
	}

	.background-decorations {
		.brand-section {
			left: 15%;
			width: 40%;
		}
	}
}

@media (max-width: 768px) {
	.main-content {
		justify-content: center;
		padding: 20px;
	}

	.login-card {
		width: 100%;
		max-width: 400px;
		min-height: auto;
	}

	.top-header .header-content {
		padding: 0 20px;

		.brand-info {
			.brand-name {
				font-size: 20px;
			}

			.brand-subtitle {
				font-size: 14px;
			}
		}
	}

	.background-decorations {
		.brand-section {
			display: none;
		}

		.background-image {
			opacity: 0.3;
		}
	}
}
</style>
